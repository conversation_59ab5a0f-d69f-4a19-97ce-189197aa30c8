# Copyright (C) 2024 Pirate DICOM Contributors

"""
Validation utilities for RT DICOM creation.

This module provides clinical and technical validation for DICOM RT objects,
ensuring both DICOM standard compliance and clinical safety.
"""

from .geometric import (
    GeometricValidator,
    validate_coordinate_bounds,
    validate_geometric_consistency,
    validate_structure_geometry,
    check_contour_closure,
    CLINICAL_GEOMETRIC_LIMITS,
)

__all__ = [
    "GeometricValidator",
    "validate_coordinate_bounds", 
    "validate_geometric_consistency",
    "validate_structure_geometry",
    "check_contour_closure",
    "CLINICAL_GEOMETRIC_LIMITS",
]