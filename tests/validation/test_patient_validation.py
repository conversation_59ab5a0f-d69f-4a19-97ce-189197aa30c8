"""
Test patient information validation functionality.

Tests comprehensive patient information validation including:
- PatientID format validation against DICOM VR constraints
- Date format validation for PatientBirthDate, StudyDate, etc.
- Bounds checking for numeric patient parameters
- Edge cases and clinical scenarios
"""

import pytest
from datetime import datetime

from pyrt_dicom.validation.patient import (
    PatientInfoValidator,
    validate_patient_info,
    validate_patient_id,
    validate_dicom_date,
    PATIENT_INFO_CONSTRAINTS,
)


class TestPatientInfoValidator:
    """Test PatientInfoValidator class functionality."""
    
    def test_init_default(self):
        """Test validator initialization with defaults."""
        validator = PatientInfoValidator()
        assert validator.strict_mode is True
        assert validator.constraints == PATIENT_INFO_CONSTRAINTS
    
    def test_init_custom(self):
        """Test validator initialization with custom settings."""
        validator = PatientInfoValidator(strict_mode=False)
        assert validator.strict_mode is False
    
    def test_valid_patient_info(self):
        """Test validation of valid patient information."""
        validator = PatientInfoValidator()
        patient_info = {
            'PatientID': 'RT001',
            'PatientName': 'Do<PERSON>^John^M',
            'PatientBirthDate': '19800101',
            'PatientSex': 'M',
            'PatientAge': '043Y'
        }
        
        errors = validator.validate_patient_info(patient_info)
        assert len(errors) == 0
    
    def test_missing_required_patient_id(self):
        """Test validation fails when required PatientID is missing."""
        validator = PatientInfoValidator()
        patient_info = {
            'PatientName': 'Doe^John'
        }
        
        errors = validator.validate_patient_info(patient_info)
        assert len(errors) == 1
        assert 'PatientID is required' in errors[0]
        assert 'DICOM compliance' in errors[0]
    
    def test_empty_patient_id(self):
        """Test validation fails when PatientID is empty."""
        validator = PatientInfoValidator()
        patient_info = {
            'PatientID': '',
            'PatientName': 'Doe^John'
        }
        
        errors = validator.validate_patient_info(patient_info)
        assert len(errors) == 1
        assert 'PatientID is required' in errors[0]


class TestPatientIDValidation:
    """Test PatientID specific validation."""
    
    def test_valid_patient_ids(self):
        """Test various valid PatientID formats."""
        valid_ids = [
            'RT001',
            'PATIENT_123',
            'P-001',
            'Study.001',
            '12345',
            'A' * 64  # Maximum length
        ]
        
        for patient_id in valid_ids:
            errors = validate_patient_id(patient_id)
            assert len(errors) == 0, f"PatientID '{patient_id}' should be valid"
    
    def test_invalid_patient_id_length(self):
        """Test PatientID length validation."""
        long_id = 'A' * 65  # Exceeds maximum length
        errors = validate_patient_id(long_id)
        
        assert len(errors) == 1
        assert 'exceeds maximum length' in errors[0]
        assert '64 characters' in errors[0]
    
    def test_invalid_patient_id_characters(self):
        """Test PatientID character validation."""
        invalid_ids = [
            'RT@001',  # @ not allowed
            'RT#001',  # # not allowed
            'RT$001',  # $ not allowed
            'RT%001',  # % not allowed
        ]
        
        for patient_id in invalid_ids:
            errors = validate_patient_id(patient_id)
            assert len(errors) == 1
            assert 'invalid characters' in errors[0]
            assert 'DICOM VR LO' in errors[0]


class TestDateValidation:
    """Test DICOM date validation."""
    
    def test_valid_dates(self):
        """Test various valid DICOM date formats."""
        valid_dates = [
            '19800101',  # Standard date
            '20231231',  # End of year
            '20240229',  # Leap year
            '19000101',  # Early date
            '20991231',  # Future date
        ]
        
        for date_str in valid_dates:
            errors = validate_dicom_date(date_str, 'TestDate')
            assert len(errors) == 0, f"Date '{date_str}' should be valid"
    
    def test_invalid_date_format(self):
        """Test invalid date formats."""
        invalid_dates = [
            '1980-01-01',  # Wrong format (has hyphens)
            '80/01/01',    # Wrong format (has slashes)
            '19800001',    # Invalid month
            '19801301',    # Invalid month
            '19800100',    # Invalid day
            '19800132',    # Invalid day
            '1980010',     # Too short
            '198001011',   # Too long
        ]
        
        for date_str in invalid_dates:
            errors = validate_dicom_date(date_str, 'TestDate')
            assert len(errors) > 0, f"Date '{date_str}' should be invalid"
    
    def test_birth_date_future_validation(self):
        """Test that future birth dates are rejected."""
        future_year = datetime.now().year + 1
        future_date = f'{future_year}0101'
        
        validator = PatientInfoValidator()
        errors = validator._validate_date_field('PatientBirthDate', future_date)
        
        assert len(errors) == 1
        assert 'cannot be in the future' in errors[0]
    
    def test_birth_date_too_old_validation(self):
        """Test that unreasonably old birth dates are rejected."""
        old_date = '18000101'  # 200+ years ago
        
        validator = PatientInfoValidator()
        errors = validator._validate_date_field('PatientBirthDate', old_date)
        
        assert len(errors) == 1
        assert 'unreasonably old' in errors[0]


class TestPatientNameValidation:
    """Test PatientName validation."""
    
    def test_valid_patient_names(self):
        """Test various valid PatientName formats."""
        validator = PatientInfoValidator()
        valid_names = [
            'Doe^John',
            'Doe^John^M',
            'Doe^John^Middle^Dr^Jr',
            'Smith',  # Single component
            'O\'Brien^Patrick',  # Apostrophe
            'Van Der Berg^Hans',  # Spaces
        ]
        
        for name in valid_names:
            errors = validator._validate_field('PatientName', name)
            assert len(errors) == 0, f"PatientName '{name}' should be valid"
    
    def test_invalid_patient_name_length(self):
        """Test PatientName length validation."""
        validator = PatientInfoValidator()
        long_name = 'A' * 65  # Exceeds maximum length
        
        errors = validator._validate_field('PatientName', long_name)
        assert len(errors) == 1
        assert 'exceeds maximum length' in errors[0]


class TestPatientSexValidation:
    """Test PatientSex validation."""
    
    def test_valid_patient_sex_values(self):
        """Test valid PatientSex values."""
        validator = PatientInfoValidator()
        valid_values = ['M', 'F', 'O', '']
        
        for sex in valid_values:
            errors = validator._validate_field('PatientSex', sex)
            assert len(errors) == 0, f"PatientSex '{sex}' should be valid"
    
    def test_invalid_patient_sex_values(self):
        """Test invalid PatientSex values."""
        validator = PatientInfoValidator()
        invalid_values = ['Male', 'Female', 'X', 'U', 'm', 'f']
        
        for sex in invalid_values:
            errors = validator._validate_field('PatientSex', sex)
            assert len(errors) == 1
            assert 'invalid value' in errors[0]
            assert 'Valid values: [\'M\', \'F\', \'O\', \'\']' in errors[0]


class TestPatientAgeValidation:
    """Test PatientAge validation."""
    
    def test_valid_patient_ages(self):
        """Test various valid PatientAge formats."""
        validator = PatientInfoValidator()
        valid_ages = [
            '001D',  # 1 day
            '052W',  # 52 weeks
            '024M',  # 24 months
            '065Y',  # 65 years
            '100Y',  # 100 years
        ]
        
        for age in valid_ages:
            errors = validator._validate_field('PatientAge', age)
            assert len(errors) == 0, f"PatientAge '{age}' should be valid"
    
    def test_invalid_patient_age_format(self):
        """Test invalid PatientAge formats."""
        validator = PatientInfoValidator()
        invalid_ages = [
            '65',     # Missing unit
            '65Y ',   # Extra space
            '65y',    # Lowercase unit
            '65Years', # Wrong unit
            '1000Y',  # Too long
            'Y65',    # Wrong order
        ]
        
        for age in invalid_ages:
            errors = validator._validate_field('PatientAge', age)
            assert len(errors) > 0, f"PatientAge '{age}' should be invalid"
    
    def test_patient_age_range_validation(self):
        """Test PatientAge range validation."""
        validator = PatientInfoValidator()
        
        # Test excessive values
        excessive_ages = [
            '400D',   # Too many days
            '600W',   # Too many weeks  
            '1300M',  # Too many months
            '200Y',   # Too many years
        ]
        
        for age in excessive_ages:
            errors = validator._validate_field('PatientAge', age)
            assert len(errors) == 1
            assert 'should not exceed' in errors[0]


class TestTimeValidation:
    """Test DICOM time validation."""
    
    def test_valid_times(self):
        """Test various valid DICOM time formats."""
        validator = PatientInfoValidator()
        valid_times = [
            '12',        # HH
            '1234',      # HHMM
            '123456',    # HHMMSS
            '123456.123', # HHMMSS.FFF
            '000000',    # Midnight
            '235959',    # End of day
        ]
        
        for time_str in valid_times:
            errors = validator._validate_time_field('StudyTime', time_str)
            assert len(errors) == 0, f"Time '{time_str}' should be valid"
    
    def test_invalid_time_ranges(self):
        """Test invalid time component ranges."""
        validator = PatientInfoValidator()
        invalid_times = [
            '25',      # Invalid hour
            '1260',    # Invalid minute
            '123460',  # Invalid second
        ]
        
        for time_str in invalid_times:
            errors = validator._validate_time_field('StudyTime', time_str)
            assert len(errors) > 0, f"Time '{time_str}' should be invalid"


class TestConvenienceFunctions:
    """Test convenience functions."""
    
    def test_validate_patient_info_function(self):
        """Test validate_patient_info convenience function."""
        patient_info = {
            'PatientID': 'RT001',
            'PatientName': 'Doe^John'
        }
        
        errors = validate_patient_info(patient_info)
        assert len(errors) == 0
        
        # Test with strict mode disabled
        errors = validate_patient_info(patient_info, strict_mode=False)
        assert len(errors) == 0
    
    def test_validate_patient_id_function(self):
        """Test validate_patient_id convenience function."""
        errors = validate_patient_id('RT001')
        assert len(errors) == 0
        
        errors = validate_patient_id('RT@001')
        assert len(errors) == 1
    
    def test_validate_dicom_date_function(self):
        """Test validate_dicom_date convenience function."""
        errors = validate_dicom_date('20231201', 'TestDate')
        assert len(errors) == 0
        
        errors = validate_dicom_date('2023-12-01', 'TestDate')
        assert len(errors) > 0


class TestEdgeCases:
    """Test edge cases and clinical scenarios."""
    
    def test_empty_values_handling(self):
        """Test handling of empty/None values."""
        validator = PatientInfoValidator()
        
        # Empty strings should not trigger validation errors (except for required fields)
        patient_info = {
            'PatientID': 'RT001',
            'PatientName': '',
            'PatientBirthDate': '',
            'PatientSex': '',
        }
        
        errors = validator.validate_patient_info(patient_info)
        assert len(errors) == 0  # Only PatientID is required
    
    def test_whitespace_handling(self):
        """Test handling of whitespace in values."""
        validator = PatientInfoValidator()
        
        # Whitespace should be stripped during validation
        patient_info = {
            'PatientID': '  RT001  ',
            'PatientName': '  Doe^John  ',
        }
        
        errors = validator.validate_patient_info(patient_info)
        assert len(errors) == 0
    
    def test_mixed_case_handling(self):
        """Test handling of mixed case in values."""
        validator = PatientInfoValidator()
        
        # PatientSex should be case-sensitive
        errors = validator._validate_field('PatientSex', 'm')  # lowercase
        assert len(errors) == 1
        assert 'invalid value' in errors[0]
